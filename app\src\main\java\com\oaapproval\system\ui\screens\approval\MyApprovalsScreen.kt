package com.oaapproval.system.ui.screens.approval

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.TextUnit
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.activity.ComponentActivity
import androidx.compose.ui.platform.LocalContext
import com.oaapproval.system.data.model.Approval
import com.oaapproval.system.data.model.ApprovalStatus
import com.oaapproval.system.ui.components.MainScaffold
import com.oaapproval.system.ui.navigation.Screen
import com.oaapproval.system.utils.DataCacheManager
import com.oaapproval.system.viewmodel.ApprovalViewModel
import com.oaapproval.system.viewmodel.AuthViewModel
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import android.util.Log
import com.oaapproval.system.OAApplication
import com.oaapproval.system.data.model.NotificationType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MyApprovalsScreen(
    navController: NavController,
    authViewModel: AuthViewModel = viewModel(LocalContext.current as ComponentActivity),
    approvalViewModel: ApprovalViewModel = viewModel(LocalContext.current as ComponentActivity)
) {
    // 收集ViewModel中的状态
    val currentUser by authViewModel.currentUser.collectAsState()
    val myApprovals by approvalViewModel.allMyApprovals.collectAsState() // 使用独立的状态
    val isLoading = approvalViewModel.isLoading
    val errorMessage = approvalViewModel.errorMessage
    
    // 数据缓存 + 后台更新策略
    LaunchedEffect(Unit) {
        authViewModel.getCurrentUser()

        // 1. 立即显示缓存数据（如果有的话）
        val cachedMyApprovals = DataCacheManager.getCachedData(DataCacheManager.Keys.MY_APPROVALS)
        if (cachedMyApprovals.isNotEmpty()) {
            Log.d("MyApprovals", "显示缓存的我的申请数据: ${cachedMyApprovals.size}条")
            approvalViewModel.updateAllMyApprovals(cachedMyApprovals) // 使用独立的更新方法
        }

        // 2. 检查是否需要刷新数据
        val needRefresh = !DataCacheManager.hasValidCache(DataCacheManager.Keys.MY_APPROVALS) ||
                         DataCacheManager.shouldForceRefresh(DataCacheManager.Keys.MY_APPROVALS)

        if (needRefresh) {
            Log.d("MyApprovals", "缓存无效或需要强制刷新，获取所有我发起的申请")
            approvalViewModel.getAllMyApprovals()
            DataCacheManager.resetForceRefresh(DataCacheManager.Keys.MY_APPROVALS)
        } else {
            Log.d("MyApprovals", "缓存有效，跳过数据加载")
        }
    }

    // 简化的备用刷新机制（仅在SSE失效时工作）
    LaunchedEffect(Unit) {
        while(true) {
            delay(60000) // 60秒检查一次
            val sseConnected = OAApplication.sseNotificationService.isConnected()
            if (!sseConnected && DataCacheManager.shouldSmartRefresh(DataCacheManager.Keys.MY_APPROVALS, sseConnected)) {
                Log.d("MyApprovals", "SSE断开，备用刷新所有我发起的申请")
                approvalViewModel.getAllMyApprovals()
            }
        }
    }

    // 统一的SSE通知处理
    LaunchedEffect(Unit) {
        Log.d("MyApprovals", "开始监听SSE通知")
        OAApplication.sseNotificationService.notificationFlow.collect { notification ->
            when (notification.type) {
                NotificationType.NEW_APPROVAL,
                NotificationType.APPROVAL_CREATED,
                NotificationType.APPROVAL_COMPLETED,
                NotificationType.APPROVAL_REJECTED,
                NotificationType.APPROVAL_TRANSFERRED,
                NotificationType.APPROVAL_DELETED -> {
                    Log.d("MyApprovals", "收到通知: ${notification.type}，刷新所有我发起的申请")
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.MY_APPROVALS)
                    approvalViewModel.getAllMyApprovals()
                }
                else -> {
                    // 其他通知类型不处理
                }
            }
        }
    }

    MainScaffold(
        navController = navController,
        title = "我的申请"
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (errorMessage != null) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color.Red
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = errorMessage,
                            color = Color.Red
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = {
                            DataCacheManager.forceRefresh(DataCacheManager.Keys.MY_APPROVALS)
                            approvalViewModel.loadMyApprovals()
                        }) {
                            Text("重试")
                        }
                    }
                }
            } else if (myApprovals.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Description,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color.Gray
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "暂无未完成的申请",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Gray
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "已完成的申请请查看历史记录",
                            fontSize = 14.sp,
                            color = Color(0xFF94A3B8)
                        )
                    }
                }
            } else {
                // 有数据时显示列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(myApprovals) { approval ->
                        MyApprovalCard(
                            approval = approval,
                            onClick = {
                                navController.navigate("${Screen.ApprovalDetail.route}/${approval.id}/${if (approval.isReading()) "READING" else "APPROVAL"}")
                            }
                        )
                    }
                    
                    item {
                        Spacer(modifier = Modifier.height(80.dp))
                    }
                }
            }
        }
    }
}

@Composable
fun MyApprovalCard(
    approval: Approval,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = approval.title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // 状态标签
                MyApprovalStatusBadge(status = approval.status)
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 申请内容
            Text(
                text = approval.description,
                fontSize = 14.sp,
                color = Color(0xFF64748B),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                lineHeight = 18.sp
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 底部信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 申请人信息（我自己）
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .background(
                                color = Color(0xFFE0E7FF),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = approval.applicant.realName?.take(1) ?: "?",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF3730A3)
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Column {
                        Text(
                            text = approval.applicant.realName ?: "未知",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF0F172A)
                        )
                        Text(
                            text = approval.applicant.position ?: "未知职位",
                            fontSize = 10.sp,
                            color = Color(0xFF94A3B8)
                        )
                    }
                }

                // 时间信息
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = approval.createTime?.substring(0, 10) ?: "未知时间",
                        fontSize = 10.sp,
                        color = Color(0xFF94A3B8)
                    )
                    Text(
                        text = when(approval.status) {
                            ApprovalStatus.PENDING -> "待处理"
                            ApprovalStatus.APPROVED -> "已通过"
                            ApprovalStatus.REJECTED -> "已拒绝"
                            ApprovalStatus.READ -> "已阅读"
                        },
                        fontSize = 10.sp,
                        color = when(approval.status) {
                            ApprovalStatus.PENDING -> Color(0xFFF59E0B)
                            ApprovalStatus.APPROVED -> Color(0xFF10B981)
                            ApprovalStatus.REJECTED -> Color(0xFFEF4444)
                            ApprovalStatus.READ -> Color(0xFF059669)
                        },
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun MyApprovalStatusBadge(status: ApprovalStatus) {
    val color = when(status) {
        ApprovalStatus.PENDING -> Color(0xFFF59E0B)
        ApprovalStatus.APPROVED -> Color(0xFF10B981)
        ApprovalStatus.REJECTED -> Color(0xFFEF4444)
        ApprovalStatus.READ -> Color(0xFF059669)
    }

    val text = when(status) {
        ApprovalStatus.PENDING -> "待审批"
        ApprovalStatus.APPROVED -> "已通过"
        ApprovalStatus.REJECTED -> "已拒绝"
        ApprovalStatus.READ -> "已阅读"
    }

    Surface(
        shape = RoundedCornerShape(4.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = text,
            color = color,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}