package com.oaapproval.system.ui.screens.approval

import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import kotlinx.coroutines.flow.collectLatest
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.oaapproval.system.data.model.*
import com.oaapproval.system.ui.components.LoadingIndicator
import com.oaapproval.system.ui.components.StatusBar
import com.oaapproval.system.ui.navigation.Screen
import com.oaapproval.system.utils.DataCacheManager
import com.oaapproval.system.viewmodel.ApprovalHistoryViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import android.util.Log
import android.widget.Toast
import androidx.compose.ui.platform.LocalContext
import androidx.compose.foundation.layout.WindowInsets
import com.oaapproval.system.ui.components.AppTopBar
import com.oaapproval.system.ui.components.TopBarAction
import com.oaapproval.system.ui.components.MainScaffold
import com.oaapproval.system.ui.utils.SystemBarsUtils
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.collect
import com.oaapproval.system.data.model.ApprovalStatus
import androidx.compose.material3.CircularProgressIndicator
import kotlinx.coroutines.delay
import com.oaapproval.system.OAApplication
import com.oaapproval.system.data.model.NotificationType


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApprovalHistoryScreen(
    navController: NavController,
    viewModel: ApprovalHistoryViewModel = viewModel()
) {

    
    var selectedFilter by remember { mutableStateOf("全部") }
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var selectedApproval by remember { mutableStateOf<Approval?>(null) }
    var isDeleting by remember { mutableStateOf(false) }
    
    // 增加错误对话框状态
    var showErrorDialog by remember { mutableStateOf(false) }
    var errorDialogTitle by remember { mutableStateOf("") }
    var errorDialogMessage by remember { mutableStateOf("") }
    var errorDialogDetails by remember { mutableStateOf("") }
    
    val scope = rememberCoroutineScope()
    
    // 获取当前用户
    val userRepository = remember { com.oaapproval.system.data.repository.UserRepository() }
    var currentUser by remember { mutableStateOf<User?>(null) }
    
    // 临时错误提示状态
    var toastMessage by remember { mutableStateOf<String?>(null) }
    val context = LocalContext.current
    
    // 获取审批历史列表
    val historyApprovals by viewModel.historyApprovals.collectAsState()
    val isLoading by remember { mutableStateOf(viewModel.isLoading) }
    val errorMessage by remember { mutableStateOf(viewModel.errorMessage) }
    
    // 显示临时提示
    toastMessage?.let {
        LaunchedEffect(it) {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            // 显示后清除消息
            toastMessage = null
        }
    }
    
    // 加载当前用户信息
    LaunchedEffect(Unit) {
        userRepository.getCurrentUser().onSuccess { user ->
            currentUser = user
            Log.d("ApprovalHistory", "当前用户: ${user.realName}, ID: ${user.id}")
        }
    }
    
    // 数据缓存 + 后台更新策略
    LaunchedEffect(Unit) {
        // 1. 立即显示缓存数据（如果有的话）
        val cachedHistory = DataCacheManager.getCachedData(DataCacheManager.Keys.HISTORY_APPROVALS)
        if (cachedHistory.isNotEmpty()) {
            Log.d("ApprovalHistory", "立即显示缓存的历史数据: ${cachedHistory.size}条")
            viewModel.updateHistoryApprovals(cachedHistory)
        }

        // 2. 智能后台更新策略
        val hasValidCache = DataCacheManager.hasValidCache(DataCacheManager.Keys.HISTORY_APPROVALS)
        val hasCachedData = DataCacheManager.hasCachedData(DataCacheManager.Keys.HISTORY_APPROVALS)

        if (!hasValidCache) {
            if (hasCachedData) {
                Log.d("ApprovalHistory", "缓存数据过期但存在，尝试后台更新")
                // 有缓存数据但过期，尝试后台更新，失败时保留缓存
                viewModel.loadHistoryApprovals()
            } else {
                Log.d("ApprovalHistory", "无缓存数据，必须加载")
                // 完全没有缓存数据，必须加载
                viewModel.loadHistoryApprovals()
            }
        } else {
            Log.d("ApprovalHistory", "缓存有效，跳过网络请求")
        }

        // 3. 检查是否需要刷新（可能在其他页面时有状态变更）
        if (DataCacheManager.shouldRefreshInBackground(DataCacheManager.Keys.HISTORY_APPROVALS)) {
            Log.d("ApprovalHistory", "检测到需要刷新，尝试更新数据")
            DataCacheManager.immediateRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)
            // 只有在有网络时才尝试刷新，失败时保留现有数据
            viewModel.loadHistoryApprovals()
        }
    }

    // 简化的备用刷新机制（仅在SSE失效时工作）
    LaunchedEffect(Unit) {
        while(true) {
            delay(60000) // 60秒检查一次
            val sseConnected = OAApplication.sseNotificationService.isConnected()
            if (!sseConnected && DataCacheManager.shouldSmartRefresh(DataCacheManager.Keys.HISTORY_APPROVALS, sseConnected)) {
                Log.d("ApprovalHistory", "SSE断开，备用刷新历史数据")
                viewModel.loadHistoryApprovals()
            }
        }
    }

    // 统一的SSE通知处理
    LaunchedEffect(Unit) {
        OAApplication.sseNotificationService.notificationFlow.collect { notification ->
            when (notification.type) {
                NotificationType.NEW_APPROVAL,
                NotificationType.APPROVAL_CREATED,
                NotificationType.APPROVAL_COMPLETED,
                NotificationType.APPROVAL_REJECTED,
                NotificationType.APPROVAL_TRANSFERRED,
                NotificationType.APPROVAL_DELETED -> {
                    Log.d("ApprovalHistory", "收到通知: ${notification.type}，刷新历史数据")
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)
                    viewModel.loadHistoryApprovals()
                }
                else -> {
                    // 其他通知类型不处理
                }
            }
        }
    }
    
    // 监听审批历史数据变化
    LaunchedEffect(historyApprovals) {
        Log.d("ApprovalHistory", "审批历史数据更新，数量: ${historyApprovals.size}")
        // 打印每个审批的关键信息，便于调试
        historyApprovals.forEach { approval ->
            Log.d("ApprovalHistory", "审批详情: ID=${approval.id}, 标题=${approval.title}, 整体状态=${approval.status}, 个人状态=${approval.userApprovalStatus}")
        }
    }
    
    // 监听加载状态变化
    LaunchedEffect(viewModel.isLoading) {
        Log.d("ApprovalHistory", "加载状态变化: ${viewModel.isLoading}")
    }
    
    // 监听错误信息变化
    LaunchedEffect(viewModel.errorMessage) {
        if (viewModel.errorMessage != null) {
            Log.e("ApprovalHistory", "错误信息: ${viewModel.errorMessage}")
        }
    }


    
    // 筛选审批列表
    val filteredApprovals = when (selectedFilter) {
        "待审批" -> historyApprovals.filter {
            it.status == ApprovalStatus.PENDING
        }
        "已通过" -> historyApprovals.filter {
            it.status == ApprovalStatus.APPROVED
        }
        "已拒绝" -> historyApprovals.filter {
            it.status == ApprovalStatus.REJECTED
        }
        else -> historyApprovals
    }



    // 临时手动刷新功能（用于测试）
    val refreshData: () -> Unit = {
        Log.d("ApprovalHistory", "手动刷新：强制重新加载历史数据")
        DataCacheManager.forceRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)
        viewModel.loadHistoryApprovals()
    }

    MainScaffold(
        navController = navController,
        title = "审批历史",
        showBackButton = false,
        actions = {
            IconButton(onClick = refreshData) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新",
                    tint = Color.White
                )
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF8FAFC))
        ) {
            // 筛选标签
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(0.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
            ) {
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    val filters = listOf("全部", "待审批", "已通过", "已拒绝")
                    items(filters) { filter ->
                        FilterChip(
                            selected = selectedFilter == filter,
                            onClick = { selectedFilter = filter },
                            label = { Text(filter) }
                        )
                    }
                }
            }
            
            // 加载指示器
            if (viewModel.isLoading) {
                LoadingIndicator(
                    modifier = Modifier.fillMaxSize(),
                    message = "正在加载审批历史..."
                )
            } else if (errorMessage != null) {
                // 错误状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color(0xFFEF4444)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "加载失败",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF64748B)
                        )
                        Text(
                            text = errorMessage ?: "未知错误",
                            fontSize = 14.sp,
                            color = Color(0xFF94A3B8),
                            modifier = Modifier.padding(top = 8.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { viewModel.loadHistoryApprovals() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF3B82F6)
                            )
                        ) {
                            Text("重新加载")
                        }
                    }
                }
            } else if (filteredApprovals.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.History,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color(0xFFCBD5E1)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "暂无审批记录",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF64748B)
                        )
                        Text(
                            text = "当前没有符合条件的审批申请",
                            fontSize = 14.sp,
                            color = Color(0xFF94A3B8),
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            } else {
                // 历史记录列表
                Box(
                    modifier = Modifier.fillMaxSize()
                ) {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(
                            start = 16.dp,
                            end = 16.dp,
                            top = 16.dp,
                            bottom = SystemBarsUtils.getSafeBottomPadding(extraPadding = 80.dp)  // 使用自适应底部间距，为底部导航栏预留空间
                        ),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        items(filteredApprovals) { approval ->
                            ApprovalHistoryCard(
                                approval = approval,
                                currentUser = currentUser,
                                onClick = {
                                    navController.navigate("${Screen.ApprovalDetail.route}/${approval.id}/${if (approval.isReading()) "READING" else "APPROVAL"}")
                                },
                                onDelete = {
                                    selectedApproval = approval
                                    showDeleteConfirmDialog = true
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    // 删除确认对话框
    if (showDeleteConfirmDialog) {
        OptimizedDeleteConfirmDialog(
            isDeleting = isDeleting,
            onConfirm = {
                showDeleteConfirmDialog = false
                selectedApproval?.id?.let { approvalId ->
                    scope.launch {
                        isDeleting = true
                        Log.d("ApprovalHistory", "开始删除申请: ID=$approvalId")

                        try {
                            // 先显示删除中的提示，提升体验
                            toastMessage = "正在删除..."

                            val result = viewModel.deleteApproval(approvalId)
                            if (result.isSuccess) {
                                Log.d("ApprovalHistory", "删除成功，清除缓存并跳转回主页")

                                // 像发起审批一样，清除相关缓存并跳转到主页
                                DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_MY_APPROVALS)
                                DataCacheManager.forceRefresh(DataCacheManager.Keys.MY_APPROVALS)
                                DataCacheManager.forceRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)
                                DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_PENDING)
                                DataCacheManager.forceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)

                                // 跳转到主页，让用户看到最新状态
                                navController.navigate(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                                    popUpTo(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                                        inclusive = false
                                    }
                                }
                            } else {
                                val exception = result.exceptionOrNull()
                                Log.e("ApprovalHistory", "删除失败", exception)

                                // 特殊处理404错误
                                if (exception?.message?.contains("404") == true ||
                                    exception?.message?.contains("未找到") == true) {
                                    Log.d("ApprovalHistory", "资源不存在，视为删除成功，跳转回主页")

                                    // 像发起审批一样，清除相关缓存并跳转到主页
                                    DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_MY_APPROVALS)
                                    DataCacheManager.forceRefresh(DataCacheManager.Keys.MY_APPROVALS)
                                    DataCacheManager.forceRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)
                                    DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_PENDING)
                                    DataCacheManager.forceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)

                                    // 跳转到主页
                                    navController.navigate(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                                        popUpTo(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                                            inclusive = false
                                        }
                                    }
                                    return@launch
                                }

                                // 解析不同的错误类型并显示详细错误信息
                                when {
                                    exception?.message?.contains("权限") == true -> {
                                        errorDialogTitle = "权限错误"
                                        errorDialogMessage = "您没有删除此申请的权限"
                                        errorDialogDetails = "仅申请人本人且申请状态为'待审批'时可以删除。\n错误详情: ${exception.message}"
                                    }
                                    exception?.message?.contains("网络") == true -> {
                                        errorDialogTitle = "网络错误"
                                        errorDialogMessage = "网络连接失败，请检查您的网络设置"
                                        errorDialogDetails = "在尝试与服务器通信时发生错误。\n错误详情: ${exception.message}"
                                    }
                                    exception?.message?.contains("服务器") == true -> {
                                        errorDialogTitle = "服务器错误"
                                        errorDialogMessage = "服务器处理请求失败"
                                        errorDialogDetails = "服务器返回了一个错误响应。\n错误详情: ${exception.message}"
                                    }
                                    exception?.message?.contains("流转") == true -> {
                                        errorDialogTitle = "流转状态错误"
                                        errorDialogMessage = "申请已处于流转状态，无法删除"
                                        errorDialogDetails = "已经开始流转的申请不能被删除。\n错误详情: ${exception.message}"
                                    }
                                    exception?.message?.contains("审批") == true -> {
                                        errorDialogTitle = "审批状态错误"
                                        errorDialogMessage = "申请已被审批，无法删除"
                                        errorDialogDetails = "已审批（通过或拒绝）的申请不能被删除。\n错误详情: ${exception.message}"
                                    }
                                    exception?.message?.contains("附件") == true -> {
                                        errorDialogTitle = "附件错误"
                                        errorDialogMessage = "删除申请附件失败"
                                        errorDialogDetails = "在尝试删除关联的附件时出错。\n错误详情: ${exception.message}"
                                    }
                                    exception?.message?.contains("数据库") == true -> {
                                        errorDialogTitle = "数据库错误"
                                        errorDialogMessage = "数据库操作失败"
                                        errorDialogDetails = "在数据库操作过程中发生错误。\n错误详情: ${exception.message}"
                                    }
                                    exception?.message?.contains("刷新") == true -> {
                                        // 特殊处理刷新列表的信息
                                        toastMessage = "列表已更新，请重新操作"
                                        // 不显示错误对话框
                                        return@launch
                                    }
                                    else -> {
                                        errorDialogTitle = "删除失败"
                                        errorDialogMessage = "无法删除审批记录"
                                        errorDialogDetails = "发生未知错误。\n错误详情: ${exception?.message ?: "未知错误"}"
                                    }
                                }

                                showErrorDialog = true
                            }
                        } catch (e: Exception) {
                            Log.e("ApprovalHistory", "删除过程中发生异常", e)
                            errorDialogTitle = "操作异常"
                            errorDialogMessage = "删除过程中发生异常"
                            errorDialogDetails = "操作被中断。\n错误详情: ${e.message ?: "未知异常"}"
                            showErrorDialog = true

                            // 遇到异常也刷新列表，确保数据一致性
                            viewModel.loadHistoryApprovals()
                        } finally {
                            isDeleting = false
                        }
                    }
                }
            },
            onDismiss = { showDeleteConfirmDialog = false }
        )
    }
    
    // 错误详情对话框
    if (showErrorDialog) {
        AlertDialog(
            onDismissRequest = { showErrorDialog = false },
            icon = { Icon(Icons.Default.Error, contentDescription = null, tint = Color(0xFFEF4444)) },
            title = { Text(errorDialogTitle) },
            text = { 
                Column {
                    Text(errorDialogMessage, fontWeight = FontWeight.Medium)
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        errorDialogDetails,
                        fontSize = 12.sp,
                        color = Color(0xFF64748B),
                        lineHeight = 16.sp
                    )
                    
                    // 删除阶段和相应错误的详细说明
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        "删除流程可能在以下阶段出错：",
                        fontWeight = FontWeight.Medium,
                        fontSize = 12.sp
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 阶段列表
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        ErrorStageItem("权限验证", "验证您是否有权限删除此申请")
                        ErrorStageItem("状态检查", "检查申请状态是否允许删除")
                        ErrorStageItem("附件清理", "删除关联的所有附件文件")
                        ErrorStageItem("流转记录清理", "删除相关的流转历史记录")
                        ErrorStageItem("评论清理", "删除所有相关的评论")
                        ErrorStageItem("申请删除", "从数据库中移除申请记录")
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = { showErrorDialog = false },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF3B82F6)
                    )
                ) {
                    Text("我知道了")
                }
            }
        )
    }
}

@Composable
private fun ErrorStageItem(title: String, description: String) {
    Row(
        modifier = Modifier.fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(Color(0xFF94A3B8), shape = CircleShape)
                .align(Alignment.CenterVertically)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column {
            Text(
                text = title,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF334155)
            )
            Text(
                text = description,
                fontSize = 10.sp,
                color = Color(0xFF64748B)
            )
        }
    }
}

@Composable
private fun CustomFilterChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.clickable { onClick() },
        shape = RoundedCornerShape(20.dp),
        color = if (isSelected) Color(0xFF3B82F6) else Color(0xFFF1F5F9)
    ) {
        Text(
            text = text,
            color = if (isSelected) Color.White else Color(0xFF64748B),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )
    }
}

@Composable
private fun ApprovalHistoryCard(
    approval: Approval,
    currentUser: User?,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    // 判断当前用户是否可以删除此申请
    val canDelete = currentUser?.id == approval.applicant.id && 
                   approval.status == ApprovalStatus.PENDING

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = approval.title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    

                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 状态标签 - 显示用户个人状态
                    UserApprovalStatusBadge(approval = approval)
                    
                    // 删除按钮（只有待处理状态的申请才显示）
                    if (canDelete) {
                        IconButton(
                            onClick = { onDelete() },
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "删除",
                                tint = Color(0xFFEF4444),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 申请内容
            Text(
                text = approval.description,
                fontSize = 14.sp,
                color = Color(0xFF64748B),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                lineHeight = 18.sp
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 底部信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 申请人信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .background(
                                color = Color(0xFFE0E7FF),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = approval.applicant.realName?.take(1) ?: "?",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF3730A3)
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Column {
                        Text(
                            text = approval.applicant.realName ?: "未知",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF0F172A)
                        )
                        Text(
                            text = approval.applicant.position ?: "未知职位",
                            fontSize = 10.sp,
                            color = Color(0xFF94A3B8)
                        )
                    }
                }

                // 时间信息
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = approval.createTime?.substring(0, 10) ?: "未知时间",
                        fontSize = 10.sp,
                        color = Color(0xFF94A3B8)
                    )
                    Text(
                        text = getOverallApprovalStatusText(approval.status),
                        fontSize = 10.sp,
                        color = getOverallApprovalStatusColor(approval.status)
                    )
                }
            }


        }
    }
}

@Composable
private fun ApprovalStatusBadge(status: ApprovalStatus) {
    val color = when(status) {
        ApprovalStatus.PENDING -> Color(0xFFF59E0B)
        ApprovalStatus.APPROVED -> Color(0xFF10B981)
        ApprovalStatus.REJECTED -> Color(0xFFEF4444)
        else -> Color(0xFF94A3B8)
    }
    
    val text = when(status) {
        ApprovalStatus.PENDING -> "待审批"
        ApprovalStatus.APPROVED -> "已通过"
        ApprovalStatus.REJECTED -> "已拒绝"
        else -> "未知"
    }
    
    Surface(
        shape = RoundedCornerShape(4.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = text,
            color = color,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

// 用户个人审批状态徽章
@Composable
private fun UserApprovalStatusBadge(approval: Approval) {
    // 添加调试日志
    println("🔍 UserApprovalStatusBadge - 审批ID: ${approval.id}")
    println("📊 userApprovalStatus: '${approval.userApprovalStatus}'")
    println("📊 整体状态: ${approval.status}")

    val color = getUserApprovalStatusColor(approval)
    val text = getUserApprovalStatusText(approval)

    println("✅ 最终徽章显示: '$text' (颜色: $color)")

    Surface(
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f),
        border = BorderStroke(1.dp, color.copy(alpha = 0.3f))
    ) {
        Text(
            text = text,
            fontSize = 10.sp,
            fontWeight = FontWeight.Medium,
            color = color,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

// 获取整体审批状态文本
private fun getOverallApprovalStatusText(status: ApprovalStatus): String {
    return when (status) {
        ApprovalStatus.PENDING -> "全部进行中"
        ApprovalStatus.APPROVED -> "全部通过"
        ApprovalStatus.REJECTED -> "全部拒绝"
        else -> "未知状态"
    }
}

// 获取整体审批状态颜色
private fun getOverallApprovalStatusColor(status: ApprovalStatus): Color {
    return when (status) {
        ApprovalStatus.PENDING -> Color(0xFF3B82F6)  // 蓝色 - 进行中
        ApprovalStatus.APPROVED -> Color(0xFF10B981) // 绿色 - 已通过
        ApprovalStatus.REJECTED -> Color(0xFFEF4444) // 红色 - 已拒绝
        else -> Color(0xFF94A3B8) // 灰色 - 未知状态
    }
}

// 优化的删除确认弹窗
@Composable
private fun OptimizedDeleteConfirmDialog(
    isDeleting: Boolean,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    androidx.compose.ui.window.Dialog(onDismissRequest = onDismiss) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 8.dp
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 警告图标
                Box(
                    modifier = Modifier
                        .size(64.dp)
                        .background(
                            Color(0xFFFEF2F2),
                            CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = Color(0xFFEF4444),
                        modifier = Modifier.size(32.dp)
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 标题
                Text(
                    text = "确认删除",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1F2937),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )

                Spacer(modifier = Modifier.height(12.dp))

                // 主要提示文本
                Text(
                    text = "确定要删除这条审批记录吗？",
                    fontSize = 16.sp,
                    color = Color(0xFF374151),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                    lineHeight = 24.sp
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 详细说明
                Text(
                    text = "此操作不可恢复，将清除所有相关的审批记录、流转记录和附件信息。",
                    fontSize = 14.sp,
                    color = Color(0xFF6B7280),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(12.dp),
                        border = androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFD1D5DB)),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF374151)
                        )
                    ) {
                        Text(
                            text = "取消",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // 确认删除按钮
                    Button(
                        onClick = onConfirm,
                        enabled = !isDeleting,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFEF4444),
                            contentColor = Color.White,
                            disabledContainerColor = Color(0xFFFCA5A5),
                            disabledContentColor = Color.White
                        )
                    ) {
                        if (isDeleting) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "删除中...",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                        } else {
                            Text(
                                text = "确定删除",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        }
    }
}

// 获取用户个人审批状态文本
private fun getUserApprovalStatusText(approval: Approval): String {
    // 添加调试日志
    println("🔍 获取用户状态文本 - 审批ID: ${approval.id}")
    println("📊 userApprovalStatus: '${approval.userApprovalStatus}'")
    println("📊 整体状态: ${approval.status}")

    val result = when (approval.userApprovalStatus) {
        "COMPLETED" -> "已通过"  // 用户审批通过
        "REJECTED" -> "已拒绝"   // 用户审批拒绝
        "CURRENT" -> "待审批"    // 用户待处理
        null -> {
            // 用户不是审批人，根据整体状态显示
            when (approval.status) {
                ApprovalStatus.PENDING -> "进行中"
                ApprovalStatus.APPROVED -> "已通过"
                ApprovalStatus.REJECTED -> "已拒绝"
                else -> "未知状态"
            }
        }
        else -> {
            println("⚠️ 未知的userApprovalStatus: '${approval.userApprovalStatus}'")
            "待审批"
        }
    }

    println("✅ 最终显示文本: '$result'")
    return result
}

// 获取用户个人审批状态颜色
private fun getUserApprovalStatusColor(approval: Approval): Color {
    return when (approval.userApprovalStatus) {
        "COMPLETED" -> Color(0xFF10B981) // 绿色 - 已通过
        "REJECTED" -> Color(0xFFEF4444)  // 红色 - 已拒绝
        "CURRENT" -> Color(0xFFF59E0B)   // 橙色 - 待审批
        null -> {
            // 用户不是审批人，根据整体状态显示
            when (approval.status) {
                ApprovalStatus.PENDING -> Color(0xFF3B82F6)  // 蓝色 - 进行中
                ApprovalStatus.APPROVED -> Color(0xFF10B981) // 绿色 - 已通过
                ApprovalStatus.REJECTED -> Color(0xFFEF4444) // 红色 - 已拒绝
                else -> Color(0xFF94A3B8) // 灰色 - 未知状态
            }
        }
        else -> Color(0xFFF59E0B) // 橙色 - 默认待审批
    }
}