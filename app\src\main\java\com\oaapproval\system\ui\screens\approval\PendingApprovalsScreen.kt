package com.oaapproval.system.ui.screens.approval

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.activity.ComponentActivity
import androidx.compose.ui.platform.LocalContext
import com.oaapproval.system.data.model.Approval
import com.oaapproval.system.data.model.ApprovalStatus
import com.oaapproval.system.ui.components.MainScaffold
import com.oaapproval.system.ui.navigation.Screen
import com.oaapproval.system.utils.DataCacheManager
import com.oaapproval.system.viewmodel.ApprovalViewModel
import com.oaapproval.system.viewmodel.AuthViewModel
import com.oaapproval.system.OAApplication
import com.oaapproval.system.data.model.NotificationType
import com.oaapproval.system.ui.utils.SmartBottomSpacer
import android.util.Log
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PendingApprovalsScreen(
    navController: NavController,
    authViewModel: AuthViewModel = viewModel(LocalContext.current as ComponentActivity),
    approvalViewModel: ApprovalViewModel = viewModel(LocalContext.current as ComponentActivity)
) {
    // 收集ViewModel中的状态
    val currentUser by authViewModel.currentUser.collectAsState()
    val pendingApprovals by approvalViewModel.allPendingApprovals.collectAsState() // 使用独立的状态
    val isLoading = approvalViewModel.isLoading
    val errorMessage = approvalViewModel.errorMessage
    
    // 数据缓存 + 后台更新策略
    LaunchedEffect(Unit) {
        authViewModel.getCurrentUser()

        // 1. 立即显示缓存数据（如果有的话）
        val cachedPending = DataCacheManager.getCachedData(DataCacheManager.Keys.PENDING_APPROVALS)
        if (cachedPending.isNotEmpty()) {
            Log.d("PendingApprovals", "显示缓存的待办数据: ${cachedPending.size}条")
            approvalViewModel.updateAllPendingApprovals(cachedPending) // 使用独立的更新方法
        }

        // 2. 检查是否需要刷新数据
        val needRefresh = !DataCacheManager.hasValidCache(DataCacheManager.Keys.PENDING_APPROVALS) ||
                         DataCacheManager.shouldForceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)

        if (needRefresh) {
            Log.d("PendingApprovals", "缓存无效或需要强制刷新，获取所有别人发起的申请")
            approvalViewModel.getAllOthersApprovals()
            DataCacheManager.resetForceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)
        } else {
            Log.d("PendingApprovals", "缓存有效，跳过数据加载")
        }
    }

    // 简化的备用刷新机制（仅在SSE失效时工作）
    LaunchedEffect(Unit) {
        while(true) {
            delay(60000) // 60秒检查一次
            val sseConnected = OAApplication.sseNotificationService.isConnected()
            if (!sseConnected && DataCacheManager.shouldSmartRefresh(DataCacheManager.Keys.PENDING_APPROVALS, sseConnected)) {
                Log.d("PendingApprovals", "SSE断开，备用刷新所有别人发起给我的申请")
                approvalViewModel.getAllOthersApprovals()
            }
        }
    }

    // 统一的SSE通知处理
    LaunchedEffect(Unit) {
        OAApplication.sseNotificationService.notificationFlow.collect { notification ->
            when (notification.type) {
                NotificationType.NEW_APPROVAL,
                NotificationType.APPROVAL_CREATED,
                NotificationType.APPROVAL_COMPLETED,
                NotificationType.APPROVAL_REJECTED,
                NotificationType.APPROVAL_TRANSFERRED,
                NotificationType.APPROVAL_DELETED -> {
                    Log.d("PendingApprovals", "收到通知: ${notification.type}，刷新所有别人发起给我的申请")
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.PENDING_APPROVALS)
                    approvalViewModel.getAllOthersApprovals()
                }
                else -> {
                    // 其他通知类型不处理
                }
            }
        }
    }
    
    MainScaffold(
        navController = navController,
        title = "待我审批"
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (errorMessage != null) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color.Red
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = errorMessage,
                            color = Color.Red
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = {
                            DataCacheManager.forceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)
                            approvalViewModel.getAllOthersApprovals()
                        }) {
                            Text("重试")
                        }
                    }
                }
            } else if (pendingApprovals.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Inbox,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color.Gray
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "暂无别人发起给我的申请",
                            color = Color.Gray
                        )
                    }
                }
            } else {
                // 有数据时显示列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(pendingApprovals) { approval ->
                        PendingApprovalCard(
                            approval = approval,
                            onClick = {
                                navController.navigate("${Screen.ApprovalDetail.route}/${approval.id}/${if (approval.isReading()) "READING" else "APPROVAL"}")
                            }
                        )
                    }
                    
                    item {
                        SmartBottomSpacer(
                            hasBottomActions = true, // 待办页面有底部导航栏
                            extraHeight = 16.dp
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun PendingApprovalCard(
    approval: Approval,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = approval.title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // 状态标签 - 根据实际状态显示
                val (statusText, statusColor, backgroundColor) = when (approval.status) {
                    ApprovalStatus.PENDING -> Triple("待审批", Color(0xFFF59E0B), Color(0xFFF59E0B).copy(alpha = 0.1f))
                    ApprovalStatus.APPROVED -> Triple("已通过", Color(0xFF10B981), Color(0xFF10B981).copy(alpha = 0.1f))
                    ApprovalStatus.REJECTED -> Triple("已拒绝", Color(0xFFEF4444), Color(0xFFEF4444).copy(alpha = 0.1f))
                    ApprovalStatus.READ -> Triple("已阅读", Color(0xFF059669), Color(0xFF059669).copy(alpha = 0.1f))
                }

                Surface(
                    shape = RoundedCornerShape(4.dp),
                    color = backgroundColor
                ) {
                    Text(
                        text = statusText,
                        color = statusColor,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 申请内容
            Text(
                text = approval.description,
                fontSize = 14.sp,
                color = Color(0xFF64748B),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                lineHeight = 18.sp
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 底部信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 申请人信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .background(
                                color = Color(0xFFE0E7FF),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = approval.applicant.realName?.take(1) ?: "?",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF3730A3)
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Column {
                        Text(
                            text = approval.applicant.realName ?: "未知",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF0F172A)
                        )
                        Text(
                            text = approval.applicant.position ?: "未知职位",
                            fontSize = 10.sp,
                            color = Color(0xFF94A3B8)
                        )
                    }
                }

                // 时间信息
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = approval.createTime?.substring(0, 10) ?: "未知时间",
                        fontSize = 10.sp,
                        color = Color(0xFF94A3B8)
                    )

                    // 根据实际状态显示处理状态
                    val (processText, processColor) = when (approval.status) {
                        ApprovalStatus.PENDING -> Pair("待处理", Color(0xFFF59E0B))
                        ApprovalStatus.APPROVED -> Pair("已完成", Color(0xFF10B981))
                        ApprovalStatus.REJECTED -> Pair("已拒绝", Color(0xFFEF4444))
                        ApprovalStatus.READ -> Pair("已阅读", Color(0xFF059669))
                    }

                    Text(
                        text = processText,
                        fontSize = 10.sp,
                        color = processColor,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}