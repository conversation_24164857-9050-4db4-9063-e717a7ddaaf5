package com.oaapproval.system.ui.screens.approval

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.activity.ComponentActivity
import com.oaapproval.system.data.model.Approval
import com.oaapproval.system.data.repository.PaginatedApprovalRepository
import com.oaapproval.system.ui.components.MainScaffold
import com.oaapproval.system.ui.components.PaginatedApprovalList
import com.oaapproval.system.ui.navigation.Screen
import com.oaapproval.system.viewmodel.AuthViewModel
import com.oaapproval.system.OAApplication
import com.oaapproval.system.data.model.NotificationType
import com.oaapproval.system.data.model.LocalNotification
import androidx.compose.runtime.mutableStateOf
import kotlinx.coroutines.launch
import android.util.Log

/**
 * 分页版待审批页面
 * 使用新的分页数据管理架构
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaginatedPendingApprovalsScreen(
    navController: NavController,
    authViewModel: AuthViewModel = viewModel(LocalContext.current as ComponentActivity)
) {
    // 分页数据仓库
    val paginatedRepository = remember { PaginatedApprovalRepository() }
    
    // 收集分页状态
    val paginationState by paginatedRepository.pendingApprovalsState.collectAsState()
    
    // 协程作用域
    val scope = rememberCoroutineScope()
    
    // 当前用户
    val currentUser by authViewModel.currentUser.collectAsState()
    
    // 页面首次加载
    LaunchedEffect(Unit) {
        Log.d("PaginatedPendingApprovals", "页面首次加载，开始获取待审批数据")
        paginatedRepository.loadPendingApprovals()
    }
    
    // 处理审批操作后的刷新
    LaunchedEffect(navController.currentBackStackEntry) {
        // 监听从审批详情页返回，刷新数据
        val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
        val shouldRefresh = savedStateHandle?.get<Boolean>("should_refresh_pending") ?: false
        
        if (shouldRefresh) {
            Log.d("PaginatedPendingApprovals", "检测到需要刷新，重新加载数据")
            paginatedRepository.loadPendingApprovals(refresh = true)
            savedStateHandle?.set("should_refresh_pending", false)
        }
    }
    
    MainScaffold(
        title = "待我审批",
        navController = navController,
        showBackButton = true,
        actions = {
            IconButton(onClick = {
                scope.launch {
                    Log.d("PaginatedPendingApprovals", "用户点击刷新按钮")
                    paginatedRepository.loadPendingApprovals(refresh = true)
                }
            }) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新"
                )
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 统计信息卡片
            if (paginationState.totalElements > 0) {
                StatisticsCard(
                    totalCount = paginationState.totalElements,
                    currentPageCount = paginationState.data.size,
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            // 分页审批列表
            PaginatedApprovalList(
                state = paginationState,
                onRefresh = {
                    scope.launch {
                        Log.d("PaginatedPendingApprovals", "下拉刷新触发")
                        paginatedRepository.loadPendingApprovals(refresh = true)
                    }
                },
                onLoadMore = {
                    scope.launch {
                        Log.d("PaginatedPendingApprovals", "加载更多触发")
                        paginatedRepository.loadMorePendingApprovals()
                    }
                },
                onApprovalClick = { approval ->
                    Log.d("PaginatedPendingApprovals", "点击审批项: ${approval.id}")
                    navController.navigate("${Screen.ApprovalDetail.route}/${approval.id}/${if (approval.isReading()) "READING" else "APPROVAL"}")
                },
                onRetry = {
                    scope.launch {
                        Log.d("PaginatedPendingApprovals", "重试加载")
                        paginatedRepository.loadPendingApprovals()
                    }
                },
                emptyMessage = "暂无待审批的申请",
                modifier = Modifier.weight(1f),
                itemContent = { approval ->
                    PendingApprovalItem(
                        approval = approval,
                        currentUser = currentUser,
                        onClick = {
                            navController.navigate("${Screen.ApprovalDetail.route}/${approval.id}/${if (approval.isReading()) "READING" else "APPROVAL"}")
                        },
                        onQuickApprove = { approvalId, action ->
                            scope.launch {
                                Log.d("PaginatedPendingApprovals", "快速审批: $approvalId, 操作: $action")
                                // 这里可以添加快速审批逻辑
                                // 审批完成后刷新列表
                                paginatedRepository.loadPendingApprovals(refresh = true)
                                
                                // 发送通知
                                try {
                                    val notification = LocalNotification(
                                        id = "quick_approval_${System.currentTimeMillis()}",
                                        type = if (action == "approve") NotificationType.APPROVAL_COMPLETED else NotificationType.APPROVAL_REJECTED,
                                        title = "审批完成",
                                        message = "您已${if (action == "approve") "通过" else "拒绝"}了一个审批申请",
                                        timestamp = System.currentTimeMillis(),
                                        approvalId = approvalId,
                                        actionRequired = false
                                    )
                                    OAApplication.notificationManager.showNotification(notification)
                                } catch (e: Exception) {
                                    Log.e("PaginatedPendingApprovals", "发送通知失败", e)
                                }
                            }
                        }
                    )
                }
            )
        }
    }
}

/**
 * 统计信息卡片
 */
@Composable
private fun StatisticsCard(
    totalCount: Long,
    currentPageCount: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "待审批总数",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "$totalCount 条",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "当前显示",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "$currentPageCount 条",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}

/**
 * 待审批项组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PendingApprovalItem(
    approval: Approval,
    currentUser: com.oaapproval.system.data.model.User?,
    onClick: () -> Unit,
    onQuickApprove: (String, String) -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 标题和紧急程度
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = approval.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.weight(1f)
                )
                
                // 临时移除优先级检查，因为Approval模型中没有priority字段
                if (false) { // 暂时禁用紧急标签
                    Surface(
                        color = MaterialTheme.colorScheme.errorContainer,
                        shape = MaterialTheme.shapes.small
                    ) {
                        Text(
                            text = "紧急",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
            }
            
            // 申请人和时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "申请人: ${approval.applicant.realName}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = approval.createTime ?: "",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 快速操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { onQuickApprove(approval.id, "reject") },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("拒绝")
                }
                
                Button(
                    onClick = { onQuickApprove(approval.id, "approve") },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("通过")
                }
            }
        }
    }
}
