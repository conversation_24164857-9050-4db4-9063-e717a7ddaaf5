package com.oaapproval.system.ui.screens.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.*
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.activity.ComponentActivity
import com.oaapproval.system.data.model.Approval
import com.oaapproval.system.data.model.ApprovalStatus
import com.oaapproval.system.ui.components.AppTopBar
import com.oaapproval.system.ui.components.StatusBar
import com.oaapproval.system.ui.components.MainScaffold
import com.oaapproval.system.ui.components.TopBarAction
import com.oaapproval.system.ui.navigation.Screen
import com.oaapproval.system.utils.DataCacheManager
import com.oaapproval.system.viewmodel.ApprovalViewModel
import com.oaapproval.system.viewmodel.AuthViewModel
import kotlinx.coroutines.launch
import android.util.Log
import com.oaapproval.system.data.api.ApiClient
import kotlinx.coroutines.delay
import kotlinx.coroutines.CoroutineScope
import com.oaapproval.system.OAApplication
import com.oaapproval.system.data.model.NotificationType

import com.oaapproval.system.ui.utils.SmartBottomSpacer
import com.oaapproval.system.ui.utils.getAdaptiveCardSpacing
import com.oaapproval.system.ui.utils.getAdaptiveContentPadding
import com.oaapproval.system.ui.utils.isSmallScreen
import com.oaapproval.system.ui.components.SmartPermissionHandler
import com.oaapproval.system.ui.components.PermissionBanner
import com.oaapproval.system.ui.components.rememberPermissionStatus
import com.oaapproval.system.ui.components.PermissionStatus
import com.oaapproval.system.MainActivity

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmployeeHomeScreen(
    navController: NavController,
    authViewModel: AuthViewModel,
    approvalViewModel: ApprovalViewModel = viewModel(LocalContext.current as ComponentActivity)
) {
    val TAG = "EmployeeHomeScreen"

    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    // 添加一个状态来跟踪是否是首次加载
    var isFirstLoad by remember { mutableStateOf(true) }

    // 权限相关状态
    val permissionStatus = rememberPermissionStatus()
    var showPermissionBanner by remember { mutableStateOf(false) }

    // 检查是否需要显示权限横幅
    LaunchedEffect(permissionStatus) {
        if (permissionStatus == PermissionStatus.DENIED) {
            // 延迟5秒后显示权限横幅，让用户先熟悉应用
            delay(5000)
            showPermissionBanner = true
        }
    }
    
    // 收集ViewModel中的状态
    val currentUser by authViewModel.currentUser.collectAsState()
    val approvals by approvalViewModel.allApprovals.collectAsState()
    val pendingApprovals by approvalViewModel.pendingApprovals.collectAsState()
    val myApprovals by approvalViewModel.myApprovals.collectAsState()
    
    // 检查是否有token
    val hasToken = remember { ApiClient.getToken() != null }


    
    // 数据缓存 + 后台更新策略 - 使用稳定的key避免重复执行
    LaunchedEffect(hasToken, currentUser?.id) { // 只在token或用户变化时执行
        if (!hasToken) {
            Log.d(TAG, "🏠 没有token，跳过数据加载")
            return@LaunchedEffect
        }
        Log.d(TAG, "🏠 ===== EmployeeHomeScreen LaunchedEffect 开始 =====")
        Log.d(TAG, "🏠 是否有token: $hasToken")
        Log.d(TAG, "🏠 当前ViewModel待办数据: ${pendingApprovals.size}条")
        Log.d(TAG, "🏠 当前ViewModel我的申请数据: ${myApprovals.size}条")

        // 1. 立即显示缓存数据（如果有的话）- 无延迟
        val cachedPending = DataCacheManager.getCachedData(DataCacheManager.Keys.HOME_PENDING)
        val cachedMyApprovals = DataCacheManager.getCachedData(DataCacheManager.Keys.HOME_MY_APPROVALS)

        // 优化缓存策略：总是优先显示缓存数据，避免空白闪烁
        if (cachedPending.isNotEmpty()) {
            val isValidCache = DataCacheManager.hasValidCache(DataCacheManager.Keys.HOME_PENDING)
            Log.d(TAG, "${if (isValidCache) "使用有效" else "使用过期"}缓存的待办审批数据: ${cachedPending.size}条")
            Log.d(TAG, "当前ViewModel待办数据: ${pendingApprovals.size}条")
            approvalViewModel.updatePendingApprovals(cachedPending)
        } else {
            Log.d(TAG, "无待办审批缓存数据，当前ViewModel数据: ${pendingApprovals.size}条")
        }

        if (cachedMyApprovals.isNotEmpty()) {
            val isValidCache = DataCacheManager.hasValidCache(DataCacheManager.Keys.HOME_MY_APPROVALS)
            Log.d(TAG, "${if (isValidCache) "使用有效" else "使用过期"}缓存的我的申请数据: ${cachedMyApprovals.size}条")
            Log.d(TAG, "当前ViewModel我的申请数据: ${myApprovals.size}条")
            approvalViewModel.updateMyApprovals(cachedMyApprovals)
        } else {
            Log.d(TAG, "无我的申请缓存数据，当前ViewModel数据: ${myApprovals.size}条")
        }

        // 2. 获取用户信息（并行进行）
        authViewModel.getCurrentUser()

        // 3. 后台静默更新（如果数据过期或没有缓存）
        val needLoadPending = !DataCacheManager.hasValidCache(DataCacheManager.Keys.HOME_PENDING) ||
                              (cachedPending.isEmpty() && pendingApprovals.isEmpty())
        val needLoadMyApprovals = !DataCacheManager.hasValidCache(DataCacheManager.Keys.HOME_MY_APPROVALS) ||
                                 (cachedMyApprovals.isEmpty() && myApprovals.isEmpty())

        if (needLoadPending) {
            Log.d(TAG, "后台加载待办审批数据（缓存无效/过期或数据为空）- 只显示待审批状态")
            approvalViewModel.loadPendingApprovals() // 主页只显示待审批的申请
        } else {
            Log.d(TAG, "待办审批数据缓存有效且有数据，跳过加载")
        }

        if (needLoadMyApprovals) {
            Log.d(TAG, "后台加载我的申请数据（缓存无效/过期或数据为空）- 主页只显示待审批状态")
            approvalViewModel.getMyPendingApprovals() // 主页只显示待审批的申请
        } else {
            Log.d(TAG, "我的申请数据缓存有效且有数据，跳过加载")
        }

        Log.d(TAG, "🏠 ===== EmployeeHomeScreen LaunchedEffect 结束 =====")
        Log.d(TAG, "🏠 最终ViewModel待办数据: ${pendingApprovals.size}条")
        Log.d(TAG, "🏠 最终ViewModel我的申请数据: ${myApprovals.size}条")
    }

    // 监听数据变化，记录日志
    LaunchedEffect(pendingApprovals.size) {
        Log.d(TAG, "🔍 待办审批数据变化: ${pendingApprovals.size}条")
    }

    LaunchedEffect(myApprovals.size) {
        Log.d(TAG, "🔍 我的申请数据变化: ${myApprovals.size}条")
    }



    // 统一的SSE通知处理
    LaunchedEffect(Unit) {
        Log.d(TAG, "🏠 首页开始监听SSE通知")
        OAApplication.sseNotificationService.notificationFlow.collect { notification ->
            when (notification.type) {
                NotificationType.NEW_APPROVAL,
                NotificationType.APPROVAL_CREATED,
                NotificationType.APPROVAL_COMPLETED,
                NotificationType.APPROVAL_REJECTED,
                NotificationType.APPROVAL_TRANSFERRED,
                NotificationType.APPROVAL_DELETED -> {
                    Log.d(TAG, "收到通知: ${notification.type}，刷新数据")
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.HOME_PENDING)
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.HOME_MY_APPROVALS)
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.PENDING_APPROVALS)
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.MY_APPROVALS)
                    // 同时标记历史数据需要刷新，确保切换到历史页面时能看到最新状态
                    DataCacheManager.immediateRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)
                    // 主页：只刷新待审批的申请
                    approvalViewModel.loadPendingApprovals()
                    approvalViewModel.getMyPendingApprovals()
                    // 查看全部页面：刷新所有状态的申请（通过缓存失效触发）
                }
                else -> {
                    // 其他通知类型不处理
                }
            }
        }
    }
    
    // 简化的备用刷新机制（仅在SSE失效时工作）
    LaunchedEffect(Unit) {
        while(true) {
            delay(60000) // 60秒检查一次
            val sseConnected = OAApplication.sseNotificationService.isConnected()
            if (!sseConnected && DataCacheManager.shouldSmartRefresh(DataCacheManager.Keys.HOME_PENDING, sseConnected)) {
                Log.d(TAG, "SSE断开，备用刷新待办数据")
                approvalViewModel.loadPendingApprovals() // 主页只显示待审批的申请
            }
            if (!sseConnected && DataCacheManager.shouldSmartRefresh(DataCacheManager.Keys.HOME_MY_APPROVALS, sseConnected)) {
                Log.d(TAG, "SSE断开，备用刷新我的申请数据")
                approvalViewModel.getMyPendingApprovals()
            }
        }
    }
    


    // 移除重复的SSE监听器，统一在第一个监听器中处理所有通知

    // 监听currentUser变化
    LaunchedEffect(currentUser) {
        Log.d(TAG, "currentUser变化: ${currentUser?.realName ?: "null"}")
    }
    
    // 如果没有登录用户，跳转到登录页面
    if (currentUser == null) {
        Log.d(TAG, "没有检测到登录用户，hasToken=$hasToken, isLoading=${authViewModel.isLoading}")

        // 如果没有token，立即跳转到登录页面
        if (!hasToken) {
            Log.d(TAG, "未检测到token，立即跳转到登录页面")
            LaunchedEffect(Unit) {
                navController.navigate(Screen.Login.route) {
                    popUpTo(0) { inclusive = true }
                }
            }
            return
        }

        // 如果有token但没有用户信息，显示加载状态
        // 但设置超时机制，避免无限等待
        var timeoutReached by remember { mutableStateOf(false) }
        var initialLoadAttempted by remember { mutableStateOf(false) }

        // 监听用户信息变化，如果成功加载则取消超时
        LaunchedEffect(currentUser) {
            if (currentUser != null && initialLoadAttempted) {
                Log.d(TAG, "用户信息已加载: ${currentUser?.realName}，取消超时机制")
                timeoutReached = false // 重置超时状态
            }
        }

        LaunchedEffect(hasToken) {
            if (hasToken && !timeoutReached && !initialLoadAttempted) {
                initialLoadAttempted = true
                Log.d(TAG, "开始初始用户信息加载，设置超时机制")

                // 先尝试从缓存恢复用户信息
                authViewModel.getCurrentUser()

                // 设置15秒超时（增加超时时间，给缓存恢复更多时间）
                kotlinx.coroutines.delay(15000)

                // 15秒后如果还没有用户信息，才认为是真正的超时
                if (currentUser == null && !timeoutReached) {
                    timeoutReached = true
                    Log.w(TAG, "获取用户信息超时，可能token已过期")
                    // 清除可能无效的token并跳转到登录页
                    authViewModel.logout()
                    navController.navigate(Screen.Login.route) {
                        popUpTo(0) { inclusive = true }
                    }
                } else {
                    Log.d(TAG, "用户信息加载成功或已取消超时机制")
                }
            }
        }

        if (!timeoutReached) {
            // 显示加载指示器
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
                Text(
                    text = "正在检查登录状态...",
                    modifier = Modifier.padding(top = 48.dp)
                )
            }
        }
        return
    }
    
    Log.d(TAG, "当前用户: ${currentUser?.realName}, 职位: ${currentUser?.position}")
    Log.d(TAG, "待办审批数量: ${pendingApprovals.size}, 我的申请数量: ${myApprovals.size}")

    MainScaffold(
        navController = navController,
        title = "首页",
        showBackButton = false
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 权限横幅
            PermissionBanner(
                show = showPermissionBanner,
                onRequestPermission = {
                    if (context is MainActivity) {
                        context.requestNotificationPermissionWhenNeeded()
                    }
                    showPermissionBanner = false
                },
                onDismiss = {
                    showPermissionBanner = false
                }
            )

            // 主要内容区域 - 自适应间距
            val contentPadding = getAdaptiveContentPadding()
            val cardSpacing = getAdaptiveCardSpacing()

            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(
                    start = contentPadding,
                    end = contentPadding,
                    top = contentPadding,
                    bottom = contentPadding // 基础底部间距，SmartBottomSpacer会添加额外间距
                ),
                verticalArrangement = Arrangement.spacedBy(cardSpacing)
            ) {
                // 常用功能区 - 始终显示
                item {
                    QuickActionsCard(
                        onCreateApproval = { navController.navigate(Screen.SelectLeader.route) },
                        onCreateReading = { navController.navigate(Screen.SelectApprovers.route + "?mode=reading&title=选择阅读人") }
                    )
                }
                
                // 待办提醒 - 总是显示
                item {
                    if (pendingApprovals.isNotEmpty()) {
                        PendingApprovalsCard(
                            approvals = pendingApprovals.take(3),
                            onViewAll = { navController.navigate(Screen.PendingApprovals.route) },
                            onItemClick = { approval ->
                                navController.navigate("${Screen.ApprovalDetail.route}/${approval.id}/${if (approval.isReading()) "READING" else "APPROVAL"}")
                            }
                        )
                    } else {
                        EmptyPendingApprovalsCard(
                            onViewAll = { navController.navigate(Screen.PendingApprovals.route) }
                        )
                    }
                }
                
                // 我的申请 - 总是显示
                item {
                    if (myApprovals.isNotEmpty()) {
                        MyApplicationsCard(
                            applications = myApprovals.take(3),
                            onViewAll = { navController.navigate(Screen.MyApprovals.route) },
                            onItemClick = { approval ->
                                navController.navigate("${Screen.ApprovalDetail.route}/${approval.id}/${if (approval.isReading()) "READING" else "APPROVAL"}")
                            }
                        )
                    } else {
                        EmptyMyApplicationsCard(
                            onViewAll = { navController.navigate(Screen.MyApprovals.route) }
                        )
                    }
                }
                
                // 移除SmartBottomSpacer，因为Scaffold已经正确处理了底部间距


            }
        }
    }
}

@Composable
private fun QuickActionsCard(
    onCreateApproval: () -> Unit,
    onCreateReading: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Dashboard,
                    contentDescription = null,
                    tint = Color(0xFF3B82F6),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "快捷操作",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF0F172A)
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 发起审批按钮
                QuickActionItem(
                    icon = Icons.Default.Add,
                    title = "发起审批",
                    color = Color(0xFF3B82F6),
                    onClick = onCreateApproval,
                    modifier = Modifier.weight(1f)
                )

                // 发起阅读按钮
                QuickActionItem(
                    icon = Icons.Default.Visibility,
                    title = "发起阅读",
                    color = Color(0xFF059669),
                    onClick = onCreateReading,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun QuickActionItem(
    icon: ImageVector,
    title: String,
    color: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = color,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun PendingApprovalsCard(
    approvals: List<Approval>,
    onViewAll: () -> Unit,
    onItemClick: (Approval) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = Color(0xFFF59E0B),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "待办提醒",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A)
                    )
                    
                    // 待办数量徽章
                    if (approvals.isNotEmpty()) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Surface(
                            shape = CircleShape,
                            color = Color(0xFFF59E0B),
                            modifier = Modifier.size(24.dp)
                        ) {
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier.fillMaxSize()
                            ) {
                                Text(
                                    text = "${approvals.size}",
                                    color = Color.White,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                    }
                }
                
                TextButton(onClick = onViewAll) {
                    Text(
                        text = "查看全部",
                        fontSize = 14.sp,
                        color = Color(0xFF3B82F6)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            approvals.forEach { approval ->
                PendingApprovalItem(
                    approval = approval,
                    onClick = { onItemClick(approval) }
                )
                if (approval != approvals.last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

@Composable
private fun PendingApprovalItem(
    approval: Approval,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        color = Color(0xFFFEF3C7)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 紧急指示器
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(Color(0xFFF59E0B), CircleShape)
            )
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = approval.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF0F172A)
                )
                Text(
                    text = "申请人：${approval.applicant?.realName ?: "未知"}${approval.applicant?.position?.takeIf { it.isNotBlank() }?.let { " - $it" } ?: ""}",
                    fontSize = 12.sp,
                    color = Color(0xFF92400E),
                    modifier = Modifier.padding(top = 2.dp)
                )

                // 如果有流转历史，显示流转信息
                if (approval.transferHistory?.isNotEmpty() == true) {
                    Text(
                        text = "已流转：${buildTransferChainForHome(approval)}",
                        fontSize = 11.sp,
                        color = Color(0xFF92400E),
                        modifier = Modifier.padding(top = 1.dp)
                    )
                }
            }
            
            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                contentDescription = null,
                tint = Color(0xFFF59E0B),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

@Composable
private fun MyApplicationsCard(
    applications: List<Approval>,
    onViewAll: () -> Unit,
    onItemClick: (Approval) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Assignment,
                        contentDescription = null,
                        tint = Color(0xFF3B82F6),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "我的申请",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A)
                    )
                    
                    // 申请总数
                    if (applications.isNotEmpty()) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Surface(
                            shape = CircleShape,
                            color = Color(0xFF3B82F6),
                            modifier = Modifier.size(24.dp)
                        ) {
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier.fillMaxSize()
                            ) {
                                Text(
                                    text = "${applications.size}",
                                    color = Color.White,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                    }
                }
                
                TextButton(onClick = onViewAll) {
                    Text(
                        text = "查看全部",
                        color = Color(0xFF3B82F6),
                        fontSize = 14.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            applications.forEach { approval ->
                MyApplicationItem(
                    approval = approval,
                    onClick = { onItemClick(approval) }
                )
                if (approval != applications.last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

@Composable
private fun MyApplicationItem(
    approval: Approval,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        color = Color(0xFFF8FAFC)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 状态指示器
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = when (approval.status) {
                            ApprovalStatus.PENDING -> Color(0xFFF59E0B)
                            ApprovalStatus.APPROVED -> Color(0xFF10B981)
                            ApprovalStatus.REJECTED -> Color(0xFFEF4444)
                            ApprovalStatus.READ -> Color(0xFF059669)
                        },
                        shape = CircleShape
                    )
            )
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = approval.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF0F172A)
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(top = 2.dp)
                ) {
                    Text(
                        text = approval.status.displayName,
                        fontSize = 12.sp,
                        color = when (approval.status) {
                            ApprovalStatus.PENDING -> Color(0xFFF59E0B)
                            ApprovalStatus.APPROVED -> Color(0xFF10B981)
                            ApprovalStatus.REJECTED -> Color(0xFFEF4444)
                            ApprovalStatus.READ -> Color(0xFF059669)
                        },
                        fontWeight = FontWeight.Medium
                    )

                }
                
                // 如果有流转历史，显示流转信息
                if (approval.transferHistory?.isNotEmpty() == true) {
                    Text(
                        text = "流转链路：${buildTransferChainForHome(approval)}",
                        fontSize = 11.sp,
                        color = Color(0xFF64748B),
                        modifier = Modifier.padding(top = 1.dp)
                    )
                }
            }
            
            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                contentDescription = null,
                tint = Color(0xFF94A3B8),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

@Composable
private fun EmptyStateCard(
    onCreateApproval: () -> Unit,
    onCreateReading: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(40.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Description,
                contentDescription = null,
                tint = Color(0xFF94A3B8),
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "暂无申请记录",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF64748B)
            )
            Text(
                text = "开始您的第一个审批申请吧",
                fontSize = 14.sp,
                color = Color(0xFF94A3B8),
                modifier = Modifier.padding(top = 4.dp)
            )
            Spacer(modifier = Modifier.height(20.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 发起审批按钮
                Button(
                    onClick = onCreateApproval,
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(12.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF3B82F6)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "发起审批",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }

                // 发起阅读按钮
                Button(
                    onClick = onCreateReading,
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(12.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF059669) // 绿色表示阅读
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Visibility,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "发起阅读",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun EmptyPendingApprovalsCard(
    onViewAll: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = Color(0xFFF59E0B),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "待办提醒",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A)
                    )
                }
                
                TextButton(onClick = onViewAll) {
                    Text(
                        text = "查看全部",
                        fontSize = 14.sp,
                        color = Color(0xFF3B82F6)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 空状态提示
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "暂无待办审批",
                    fontSize = 14.sp,
                    color = Color(0xFF94A3B8)
                )
            }
        }
    }
}

@Composable
private fun EmptyMyApplicationsCard(
    onViewAll: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Assignment,
                        contentDescription = null,
                        tint = Color(0xFF3B82F6),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "我的申请",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A)
                    )
                }
                
                TextButton(onClick = onViewAll) {
                    Text(
                        text = "查看全部",
                        color = Color(0xFF3B82F6),
                        fontSize = 14.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 空状态提示
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "暂无待审批的申请",
                    fontSize = 14.sp,
                    color = Color(0xFF94A3B8)
                )
            }
        }
    }
}

// 构建流转链路显示字符串（首页用）
private fun buildTransferChainForHome(approval: Approval): String {
    if (approval.transferHistory == null || approval.transferHistory.isEmpty()) {
        return approval.approver?.realName ?: "待分配"
    }

    val chain = mutableListOf<String>()

    // 添加原始审批人（第一次流转的fromUser）
    val originalApprover = approval.transferHistory?.firstOrNull()?.fromUser
    if (originalApprover != null) {
        chain.add(originalApprover.realName ?: "未知")
    }

    // 添加所有流转接收人
    approval.transferHistory?.forEach { transfer ->
        val toUserName = transfer.toUser?.realName
        if (!toUserName.isNullOrBlank()) {
            chain.add(toUserName)
        } else {
            chain.add("待分配")
        }
    }

    return chain.joinToString(" → ")
}